import time
import struct
import socket
import threading
import yaml
from pyModbusTCP.client import ModbusClient
from lib_loggers import set_logger

# Load configuration
try:
    with open("config.yaml") as config_file:
        config = yaml.safe_load(config_file)
except FileNotFoundError as e:
    raise FileNotFoundError('The config.yaml file should be present.')

logger = set_logger()

def check_network_connectivity(host, port, timeout=3):
    """Test if a device is reachable on the network"""
    # TODO: Implement socket-based connectivity test
    # Hint: Use socket.create_connection() with timeout
    # Return True if connection successful, False otherwise
    try:
        # Your code here
        pass
    except Exception as e:
        logger.debug(f"Connection test failed for {host}:{port} - {e}")
        return False

def decode_float_from_registers(register_pair):
    """Convert two Modbus registers back to IEEE 754 float"""
    # TODO: Implement this function
    # This is the reverse of what the simulator does
    # Hint: Look at the actual project's decode_for_float method
    # Input: list of 2 registers [high_word, low_word]
    # Output: float value
    
    if len(register_pair) != 2:
        raise ValueError("Expected exactly 2 registers for float conversion")
    
    # Your code here - convert registers back to float
    # Steps:
    # 1. Combine the two 16-bit registers into a 32-bit value
    # 2. Pack as big-endian unsigned int
    # 3. Unpack as big-endian float
    pass

def read_power_meter_data(meter_name, meter_config):
    """Read all configured registers from a power meter"""
    logger.info(f"Connecting to {meter_name} at {meter_config['ip_address']}:{meter_config['port']}")
    
    # TODO: Implement Modbus client connection
    # 1. Create ModbusClient instance
    # 2. Test connectivity first
    # 3. Read each register defined in config
    # 4. Convert raw data to meaningful values
    # 5. Log the results
    # 6. Handle connection errors gracefully
    
    ip_address = meter_config['ip_address']
    port = meter_config['port']
    registers = meter_config['registers']
    
    # Test connectivity first
    if not check_network_connectivity(ip_address, port):
        logger.error(f"{meter_name}: Cannot reach device at {ip_address}:{port}")
        return None
    
    # Create Modbus client
    # Your code here
    
    try:
        # Your code here - read each register and convert to float
        # For each register in the config:
        # 1. Read 2 registers (for IEEE 754 float)
        # 2. Convert to float using decode_float_from_registers()
        # 3. Log the result with proper units
        pass
        
    except Exception as e:
        logger.error(f"{meter_name}: Error reading data - {e}")
        return None
    finally:
        # Your code here - close connection
        pass

def monitor_single_meter(meter_name, meter_config, duration):
    """Monitor one power meter for specified duration"""
    logger.info(f"Starting monitoring for {meter_name}")
    
    # TODO: Implement continuous monitoring
    # 1. Connect to power meter
    # 2. Read data at configured intervals
    # 3. Handle network failures and reconnection
    # 4. Track statistics (successful reads, failures)
    # 5. Log meaningful data with proper formatting
    
    start_time = time.time()
    read_count = 0
    error_count = 0
    
    # Your monitoring loop here
    # Hints:
    # - Use time.time() to track elapsed time
    # - Use time.sleep() for intervals
    # - Count successful reads and errors
    # - Handle exceptions gracefully
    
    logger.info(f"Completed monitoring for {meter_name}: {read_count} successful reads, {error_count} errors")
    return read_count

def main():
    env_choice = 'development'  # Change to 'production' for real meters
    config_env = config[env_choice]
    
    logger.info("Starting power meter monitoring system...")
    
    # Get monitoring configuration
    meters = config_env['power_meters']
    monitoring_config = config_env['monitoring']
    
    logger.info(f"Monitoring {len(meters)} power meters")
    
    # TODO: Implement multi-meter monitoring with threading
    # 1. Create threads for each power meter (like Task 2)
    # 2. Start all monitoring threads simultaneously
    # 3. Handle graceful shutdown
    # 4. Generate summary statistics
    
    # For now, test with single meter (remove this when implementing threading)
    if meters:
        first_meter_name = list(meters.keys())[0]
        first_meter_config = meters[first_meter_name]
        logger.info("Testing single meter connection...")
        read_power_meter_data(first_meter_name, first_meter_config)
    
    logger.info("Power meter monitoring completed")

if __name__ == "__main__":
    main()
